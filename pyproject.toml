[project]
name = "llm-grok-enhanced"
version = "1.0.0"
description = "Enhanced LLM plugin for xAI Grok models with enterprise features and advanced capabilities"
readme = "README.md"
authors = [
    {name = "Nico Bailon", email = "<EMAIL>"},
    {name = "<PERSON><PERSON><PERSON><PERSON>"}
]
license = {text = "Apache-2.0"}
keywords = ["llm", "grok", "xai", "plugin", "ai", "enhanced", "enterprise"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence"
]
dependencies = [
    "llm>=0.17",
    "httpx",
    "pydantic>=2.0",
    "typing_extensions",
    "rich>=10.0.0",
]

[project.urls]
Homepage = "https://github.com/nicobailon/llm-grok"
Documentation = "https://github.com/nicobailon/llm-grok#readme"
Repository = "https://github.com/nicobailon/llm-grok"
Changelog = "https://github.com/nicobailon/llm-grok/releases"
Issues = "https://github.com/nicobailon/llm-grok/issues"
CI = "https://github.com/nicobailon/llm-grok/actions"
"Original Project" = "https://github.com/Hiepler/llm-grok"

[project.entry-points.llm]
grok-enhanced = "llm_grok"

[project.optional-dependencies]
test = ["pytest", "pytest-httpx", "pytest-cov"]
dev = ["mypy", "black", "ruff", "types-click"]

[tool.setuptools.packages.find]
include = ["llm_grok", "llm_grok.*"]
exclude = ["examples", "examples.*", "tests", "tests.*", "docs"]

[tool.mypy]
python_version = "3.9"
strict = true
warn_return_any = true
warn_unused_configs = true
no_implicit_reexport = true
namespace_packages = true
show_error_codes = true
show_column_numbers = true
pretty = true

[[tool.mypy.overrides]]
module = [
    "httpx.*",
    "httpx_sse",
    "llm.*",
    "pydantic.*",
    "rich.*"
]
ignore_missing_imports = true

[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311', 'py312', 'py313']

[tool.ruff]
line-length = 100
target-version = "py39"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501", # line too long (handled by black)
    "B008", # do not perform function calls in argument defaults
    "W191", # indentation contains tabs
]
